<?php $__env->startSection('meta_tags'); ?>
    

<?php $__env->stopSection(); ?>

<?php $__env->startSection('page_title', __('User Management')); ?>

<?php $__env->startSection('css_links'); ?>
    
    <!-- DataTables css -->
    <link href="<?php echo e(asset('assets/css/custom_css/datatables/dataTables.bootstrap4.min.css')); ?>" rel="stylesheet" type="text/css" />
    <link href="<?php echo e(asset('assets/css/custom_css/datatables/datatable.css')); ?>" rel="stylesheet" type="text/css" />

    
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/select2/select2.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/bootstrap-select/bootstrap-select.css')); ?>" />
    
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/node-waves/node-waves.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/perfect-scrollbar/perfect-scrollbar.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/typeahead-js/typeahead.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/flatpickr/flatpickr.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/bootstrap-datepicker/bootstrap-datepicker.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/bootstrap-daterangepicker/bootstrap-daterangepicker.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/jquery-timepicker/jquery-timepicker.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/pickr/pickr-themes.css')); ?>" />
<?php $__env->stopSection(); ?>

<?php $__env->startSection('custom_css'); ?>
    
    <style>
    /* Custom CSS Here */
    </style>
<?php $__env->stopSection(); ?>


<?php $__env->startSection('page_name'); ?>
    <b class="text-uppercase"><?php echo e(__('All Users')); ?></b>
<?php $__env->stopSection(); ?>


<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><?php echo e(__('User Management')); ?></li>
    <li class="breadcrumb-item active"><?php echo e(__('All Users')); ?></li>
<?php $__env->stopSection(); ?>


<?php $__env->startSection('content'); ?>

<!-- Start row -->
<div class="row">
    <div class="col-md-12">
        <form action="<?php echo e(route('administration.settings.user.index')); ?>" method="get">
            <?php echo csrf_field(); ?>
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row">
                        <div class="mb-3 col-md-4">
                            <label for="team_leader_id" class="form-label"><?php echo e(__('Select Team Leader')); ?></label>
                            <select name="team_leader_id" id="team_leader_id" class="select2 form-select <?php $__errorArgs = ['team_leader_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" data-allow-clear="true">
                                <option value="" <?php echo e(is_null(request()->team_leader_id) ? 'selected' : ''); ?>><?php echo e(__('Select Team Leader')); ?></option>
                                <?php $__currentLoopData = $teamLeaders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $leader): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($leader->id); ?>" <?php echo e($leader->id == request()->team_leader_id ? 'selected' : ''); ?>>
                                        <?php echo e(get_employee_name($leader)); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            <?php $__errorArgs = ['team_leader_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <b class="text-danger"><i class="feather icon-info mr-1"></i><?php echo e($message); ?></b>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="mb-3 col-md-3">
                            <label for="role_id" class="form-label">Select Role</label>
                            <select name="role_id" id="role_id" class="select2 form-select <?php $__errorArgs = ['role_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" data-allow-clear="true">
                                <option value="" <?php echo e(is_null(request()->role_id) ? 'selected' : ''); ?>>Select Role</option>
                                <?php $__currentLoopData = $roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($role->id); ?>" <?php echo e($role->id == request()->role_id ? 'selected' : ''); ?>>
                                        <?php echo e($role->name); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            <?php $__errorArgs = ['role_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <b class="text-danger"><i class="feather icon-info mr-1"></i><?php echo e($message); ?></b>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        <div class="mb-3 col-md-3">
                            <label for="start_time" class="form-label"><?php echo e(__('Shift Start & End Time')); ?></label>
                            <div class="input-group">
                                <input type="text" id="start_time" name="start_time" value="<?php echo e(old('start_time', request()->start_time)); ?>" placeholder="HH:MM" class="form-control time-picker <?php $__errorArgs = ['start_time'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" />
                                <small class="input-group-text text-muted text-uppercase fs-tiny">To</small>
                                <input type="text" id="end_time" name="end_time" value="<?php echo e(old('end_time', request()->end_time)); ?>" placeholder="HH:MM" class="form-control time-picker <?php $__errorArgs = ['end_time'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" />
                            </div>
                            <?php $__errorArgs = ['start_time'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <b class="text-danger"><i class="feather icon-info mr-1"></i><?php echo e($message); ?></b>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            <?php $__errorArgs = ['end_time'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <b class="text-danger"><i class="feather icon-info mr-1"></i><?php echo e($message); ?></b>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        <div class="mb-3 col-md-2">
                            <label for="status" class="form-label">Select Task Status</label>
                            <select name="status" id="status" class="form-select bootstrap-select w-100 <?php $__errorArgs = ['status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"  data-style="btn-default">
                                <option value="" <?php echo e(is_null(request()->status) ? 'selected' : ''); ?>>Select Status</option>
                                <option value="Active" <?php echo e(request()->status == 'Active' ? 'selected' : ''); ?>>Active</option>
                                <option value="Inactive" <?php echo e(request()->status == 'Inactive' ? 'selected' : ''); ?>>Inactive</option>
                                <option value="Fired" <?php echo e(request()->status == 'Fired' ? 'selected' : ''); ?>>Fired</option>
                                <option value="Resigned" <?php echo e(request()->status == 'Resigned' ? 'selected' : ''); ?>>Resigned</option>
                            </select>
                            <?php $__errorArgs = ['status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <b class="text-danger"><i class="feather icon-info mr-1"></i><?php echo e($message); ?></b>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>

                    <div class="col-md-12 text-end">
                        <a href="<?php echo e(route('administration.settings.user.advance_filter.index')); ?>" class="text-bold text-primary me-2">
                            <span class="tf-icon ti ti-filter ti-xs"></span>
                            <?php echo e(__('Advance Filter')); ?>

                        </a>
                        <?php if(request()->role_id || request()->status): ?>
                            <a href="<?php echo e(route('administration.settings.user.index')); ?>" class="btn btn-danger confirm-warning">
                                <span class="tf-icon ti ti-refresh ti-xs me-1"></span>
                                Reset Filters
                            </a>
                        <?php endif; ?>
                        <button type="submit" class="btn btn-primary">
                            <span class="tf-icon ti ti-filter ti-xs me-1"></span>
                            <?php echo e(__('Filter Users')); ?>

                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card mb-4">
            <div class="card-header header-elements">
                <h5 class="mb-0">
                    <span>All</span>
                    <span><?php echo e(request()->status ?? 'Active'); ?></span>
                    <span><?php echo e(request()->role_id ? show_plural(show_role(request()->role_id)) : 'Users'); ?></span>
                </h5>

                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('User Create')): ?>
                    <div class="card-header-elements ms-auto">
                        <a href="<?php echo e(route('administration.settings.user.create')); ?>" class="btn btn-sm btn-primary">
                            <span class="tf-icon ti ti-plus ti-xs me-1"></span>
                            Create New User
                        </a>
                    </div>
                <?php endif; ?>
            </div>
            <div class="card-body">
                <div class="table-responsive-md table-responsive-sm w-100">
                    <table class="table data-table table-bordered">
                        <thead>
                            <tr>
                                <th>Sl.</th>
                                <th>Employee ID</th>
                                <th>Name</th>
                                <th>Email & Shift</th>
                                <th class="text-center">Religion, Gender & Blood Group</th>
                                <th class="text-center">Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <th>#<?php echo e(serial($users, $key)); ?></th>
                                    <th>
                                        <b class="text-primary fs-5"><?php echo e($user->userid); ?></b>
                                        <br>
                                        <small>
                                            <a href="<?php echo e(route('administration.settings.user.user_interaction.index', ['user' => $user])); ?>" target="_blank" class="mb-1 text-capitalize text-bold text-dark" title="Team Leader">
                                                <?php if(isset($user->active_team_leader)): ?>
                                                    <?php echo e($user->active_team_leader->employee->alias_name); ?>

                                                <?php else: ?>
                                                    <?php echo e(__('Not Assigned')); ?>

                                                <?php endif; ?>
                                            </a>
                                        </small>
                                    </th>
                                    <td>
                                        <?php echo show_user_name_and_avatar($user); ?>

                                    </td>
                                    <td>
                                        <a href="mailto:<?php echo e(optional($user->employee)->official_email); ?>" class="mb-1 text-bold" title="Official Email">
                                            <?php echo e(optional($user->employee)->official_email); ?>

                                        </a>
                                        <br>
                                        <b class="text-dark" title="Current Working Shift">
                                            <?php echo e(show_time(optional($user->current_shift)->start_time). ' to '.show_time(optional($user->current_shift)->end_time)); ?>

                                        </b>
                                    </td>
                                    <td class="text-center">
                                        <b class="text-bold text-dark"><?php echo e(optional(optional($user->employee)->religion)->name); ?></b>
                                        <br>
                                        <small class="text-muted"><?php echo e(optional($user->employee)->gender); ?></small>
                                        <br>
                                        <small class="text-muted"><?php echo e(optional($user->employee)->blood_group); ?></small>
                                    </td>
                                    <td class="text-center">
                                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['User Update', 'User Delete'])): ?>
                                            <div class="d-inline-block">
                                                <a href="javascript:void(0);" class="btn btn-sm btn-icon dropdown-toggle hide-arrow" data-bs-toggle="dropdown" aria-expanded="false">
                                                    <i class="text-primary ti ti-dots-vertical"></i>
                                                </a>
                                                <div class="dropdown-menu dropdown-menu-end m-0" style="">
                                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('User Update')): ?>
                                                        <a href="<?php echo e(route('administration.settings.user.edit', ['user' => $user])); ?>" class="dropdown-item">
                                                            <i class="text-primary ti ti-pencil"></i>
                                                            Edit
                                                        </a>
                                                    <?php endif; ?>
                                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('User Delete')): ?>
                                                        <div class="dropdown-divider"></div>
                                                        <a href="<?php echo e(route('administration.settings.user.destroy', ['user' => $user])); ?>" class="dropdown-item text-danger delete-record confirm-danger">
                                                            <i class="ti ti-trash"></i>
                                                            Delete
                                                        </a>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        <?php endif; ?>
                                        <a href="<?php echo e(route('administration.settings.user.show.profile', ['user' => $user])); ?>" class="btn btn-sm btn-icon btn-primary item-edit" data-bs-toggle="tooltip" title="Show Details">
                                            <i class="ti ti-info-hexagon"></i>
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- End row -->

<?php $__env->stopSection(); ?>


<?php $__env->startSection('script_links'); ?>
    
    <!-- Datatable js -->
    <script src="<?php echo e(asset('assets/js/custom_js/datatables/jquery.dataTables.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/js/custom_js/datatables/dataTables.bootstrap4.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/js/custom_js/datatables/datatable.js')); ?>"></script>

    <script src="<?php echo e(asset('assets/js/form-layouts.js')); ?>"></script>

    <script src="<?php echo e(asset('assets/vendor/libs/select2/select2.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/vendor/libs/bootstrap-select/bootstrap-select.js')); ?>"></script>
    
    <script src="<?php echo e(asset('assets/vendor/libs/moment/moment.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/vendor/libs/flatpickr/flatpickr.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/vendor/libs/bootstrap-datepicker/bootstrap-datepicker.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/vendor/libs/bootstrap-daterangepicker/bootstrap-daterangepicker.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/vendor/libs/jquery-timepicker/jquery-timepicker.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/vendor/libs/pickr/pickr.js')); ?>"></script>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('custom_script'); ?>
    
    <script>
        $(document).ready(function () {
            $('.time-picker').flatpickr({
                enableTime: true,
                noCalendar: true
            });
        });
    </script>
    <script>
        // Custom Script Here
        $(document).ready(function() {
            $('.bootstrap-select').each(function() {
                if (!$(this).data('bs.select')) { // Check if it's already initialized
                    $(this).selectpicker();
                }
            });
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.administration.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH E:\NIGEL\LaravelProjects\laragon\www\BlueOrange\resources\views/administration/settings/user/index.blade.php ENDPATH**/ ?>